from app.repositories.customer_repository import CustomerRepository
from app.repositories.document_repository import DocumentRepository
from app.repositories.event_repository import EventRepository
from app.models.customer import Customer
from app.models.document import Document
from app import db
from app.utils.cache_utils import clear_cache_for_entity
from typing import List, Dict, Tuple
import logging

logger = logging.getLogger(__name__)

class CustomerService:
    def __init__(self):
        self.customer_repo = CustomerRepository()
        self.document_repo = DocumentRepository()
        self.event_repo = EventRepository()

    def get_all_customers(self, page: int = 1, per_page: int = 20) -> Tuple[List[Dict], int]:
        customers, total = self.customer_repo.get_all(page, per_page)
        return [customer.to_dict() for customer in customers], total

    def get_customer_by_id(self, customer_id: int) -> Dict:
        customer = self.customer_repo.get_by_id(customer_id)
        if not customer:
            raise Exception("Customer not found")
        return customer.to_dict()

    def create_customer(self, customer_data: Dict) -> Dict:
        if 'gender' in customer_data:
            Customer.validate_gender(customer_data['gender'])
        customer = self.customer_repo.create(customer_data)

        # Clear cache
        clear_cache_for_entity('customer')

        return customer.to_dict()

    def update_customer(self, customer_id: int, customer_data: Dict) -> Dict:
        if 'gender' in customer_data:
            Customer.validate_gender(customer_data['gender'])
        customer = self.customer_repo.get_by_id(customer_id)
        if not customer:
            raise Exception("Customer not found")
        updated_customer = self.customer_repo.update(customer, customer_data)

        # Clear cache
        clear_cache_for_entity('customer', customer_id)

        return updated_customer.to_dict()

    def get_linked_data_count(self, customer_id: int) -> Dict:
        """Get count of all data linked to a customer."""
        # First check if the customer exists
        customer = self.customer_repo.get_by_id(customer_id)
        if not customer:
            raise Exception("Customer not found")

        try:
            # Import all models that might reference this customer
            from app.models.document import Document
            from app.models.event import Event
            from app.models.customer_note import CustomerNote
            from app.models.quotation import Quotation
            from app.models.quotation_new import Quotation as QuotationNew

            # Get counts
            documents_count = Document.query.filter_by(customer_id=customer_id).count()
            events_count = Event.query.filter_by(customer_id=customer_id).count()
            notes_count = CustomerNote.query.filter_by(customer_id=customer_id).count()
            quotations_count = Quotation.query.filter_by(customer_id=customer_id).count()
            quotations_new_count = QuotationNew.query.filter_by(customer_id=customer_id).count()

            # Count sub-documents
            sub_documents_count = 0
            documents = Document.query.filter_by(customer_id=customer_id).all()
            for document in documents:
                sub_documents_count += Document.query.filter_by(related_document_id=document.id).count()

            return {
                "customer_name": customer.name,
                "documents": documents_count,
                "sub_documents": sub_documents_count,
                "events": events_count,
                "notes": notes_count,
                "quotations": quotations_count + quotations_new_count,
                "total_items": documents_count + sub_documents_count + events_count + notes_count + quotations_count + quotations_new_count
            }

        except Exception as e:
            logger.error(f"Error getting linked data count for customer {customer_id}: {str(e)}")
            raise Exception(f"Failed to get linked data count: {str(e)}")

    def get_customer_deletion_info(self, customer_id: int) -> Dict:
        """
        Get detailed information about what will be deleted when deleting a customer.

        Args:
            customer_id: ID of the customer to check

        Returns:
            Dict with detailed breakdown of related data that will be deleted
        """
        customer = self.customer_repo.get_by_id(customer_id)
        if not customer:
            raise Exception("Customer not found")

        try:
            # Import all models that might reference this customer
            from app.models.document import Document
            from app.models.event import Event
            from app.models.customer_note import CustomerNote
            from app.models.quotation import Quotation
            from app.models.quotation_new import Quotation as QuotationNew

            # Get all related data
            documents = Document.query.filter_by(customer_id=customer_id).all()
            events = Event.query.filter_by(customer_id=customer_id).all()
            notes = CustomerNote.query.filter_by(customer_id=customer_id).all()
            quotations = Quotation.query.filter_by(customer_id=customer_id).all()
            quotations_new = QuotationNew.query.filter_by(customer_id=customer_id).all()

            # Count sub-documents
            sub_documents = []
            for document in documents:
                sub_docs = Document.query.filter_by(related_document_id=document.id).all()
                sub_documents.extend(sub_docs)

            # Calculate file storage usage
            total_files = len(documents) + len(sub_documents) + len(quotations) + len(quotations_new)

            # Get document types breakdown
            document_types = {}
            for doc in documents + sub_documents:
                doc_type = doc.document_type
                if doc_type not in document_types:
                    document_types[doc_type] = 0
                document_types[doc_type] += 1

            return {
                "customer_id": customer_id,
                "customer_name": customer.name,
                "customer_code": customer.code,
                "documents": {
                    "count": len(documents),
                    "sub_documents": len(sub_documents),
                    "types": document_types,
                    "files_in_storage": len(documents) + len(sub_documents)
                },
                "events": {
                    "count": len(events),
                    "pending": len([e for e in events if e.status == "pending"]),
                    "completed": len([e for e in events if e.status == "completed"])
                },
                "notes": {
                    "count": len(notes)
                },
                "quotations": {
                    "count": len(quotations) + len(quotations_new),
                    "old_format": len(quotations),
                    "new_format": len(quotations_new),
                    "files_in_storage": len(quotations) + len(quotations_new)
                },
                "storage_impact": {
                    "total_files": total_files,
                    "warning": "All files will be permanently deleted from Firebase Storage"
                },
                "total_items": len(documents) + len(sub_documents) + len(events) + len(notes) + len(quotations) + len(quotations_new),
                "warning_message": f"⚠️ PERMANENT DELETION WARNING ⚠️\n\nDeleting customer '{customer.name}' will permanently remove:\n- {len(documents) + len(sub_documents)} documents (including files)\n- {len(events)} events\n- {len(notes)} notes\n- {len(quotations) + len(quotations_new)} quotations (including PDF files)\n\nThis action CANNOT be undone!"
            }

        except Exception as e:
            logger.error(f"Error getting deletion info for customer {customer_id}: {str(e)}")
            raise Exception(f"Failed to get customer deletion info: {str(e)}")

    def delete_customer(self, customer_id: int) -> bool:
        """
        Delete a customer and all associated data including Firebase Storage files.

        Args:
            customer_id: ID of the customer to delete

        Returns:
            bool: True if deletion was successful

        Raises:
            Exception: If deletion fails
        """
        # First check if the customer exists
        customer = self.customer_repo.get_by_id(customer_id)
        if not customer:
            raise Exception("Customer not found")

        try:
            # Import all models that might reference this customer
            from app.models.document import Document
            from app.models.event import Event
            from app.models.customer_note import CustomerNote
            from app.models.quotation import Quotation
            from app.models.quotation_new import Quotation as QuotationNew
            from app.utils.firebase import delete_file_from_storage

            # Get all related data for logging and file cleanup
            documents = Document.query.filter_by(customer_id=customer_id).all()
            events = Event.query.filter_by(customer_id=customer_id).all()
            notes = CustomerNote.query.filter_by(customer_id=customer_id).all()
            quotations = Quotation.query.filter_by(customer_id=customer_id).all()
            quotations_new = QuotationNew.query.filter_by(customer_id=customer_id).all()

            # Get sub-documents
            sub_documents = []
            for document in documents:
                sub_docs = Document.query.filter_by(related_document_id=document.id).all()
                sub_documents.extend(sub_docs)

            logger.info(f"🗑️ CUSTOMER DELETION: Starting deletion of customer {customer_id} ({customer.name})")
            logger.info(f"📊 Data to delete: {len(documents)} documents, {len(sub_documents)} sub-documents, {len(events)} events, {len(notes)} notes, {len(quotations) + len(quotations_new)} quotations")

            # Track files for cleanup
            files_to_delete = []

            # Delete all documents for this customer (including sub-documents and files)
            for document in documents:
                logger.info(f"📄 Deleting document {document.id} ({document.name}) for customer {customer_id}")

                # Add file to deletion list
                if document.file_path:
                    files_to_delete.append(document.file_path)

                # Delete any sub-documents first
                for sub_doc in sub_documents:
                    if sub_doc.related_document_id == document.id:
                        logger.info(f"📄 Deleting sub-document {sub_doc.id} for document {document.id}")
                        if sub_doc.file_path:
                            files_to_delete.append(sub_doc.file_path)
                        db.session.delete(sub_doc)

                # Then delete the document itself
                db.session.delete(document)

            # Delete all events for this customer
            for event in events:
                logger.info(f"📅 Deleting event {event.id} for customer {customer_id}")
                db.session.delete(event)

            # Delete all customer notes
            for note in notes:
                logger.info(f"📝 Deleting customer note {note.id} for customer {customer_id}")
                db.session.delete(note)

            # Delete all quotations (old model)
            for quotation in quotations:
                logger.info(f"💰 Deleting quotation {quotation.id} for customer {customer_id}")
                # Check if quotation has associated document with file
                if quotation.document and quotation.document.file_path:
                    files_to_delete.append(quotation.document.file_path)
                db.session.delete(quotation)

            # Delete all quotations (new model)
            for quotation in quotations_new:
                logger.info(f"💰 Deleting new quotation {quotation.id} for customer {customer_id}")
                # Check if quotation has associated document with file
                if quotation.document and quotation.document.file_path:
                    files_to_delete.append(quotation.document.file_path)
                db.session.delete(quotation)

            # Finally delete the customer
            logger.info(f"👤 Deleting customer {customer_id} ({customer.name})")
            db.session.delete(customer)

            # Commit database changes first
            db.session.commit()
            logger.info(f"✅ Database deletion completed for customer {customer_id}")

            # Now delete files from Firebase Storage
            failed_file_deletions = []
            for file_path in files_to_delete:
                try:
                    delete_file_from_storage(file_path)
                    logger.info(f"🗑️ Deleted file from storage: {file_path}")
                except Exception as file_error:
                    logger.warning(f"⚠️ Failed to delete file from storage: {file_path} - {str(file_error)}")
                    failed_file_deletions.append(file_path)

            # Clear cache
            clear_cache_for_entity('customer', customer_id)

            if failed_file_deletions:
                logger.warning(f"⚠️ Customer {customer_id} deleted but {len(failed_file_deletions)} files could not be removed from storage: {failed_file_deletions}")
            else:
                logger.info(f"✅ Successfully deleted customer {customer_id} and all associated data including {len(files_to_delete)} files from storage")

            return True

        except Exception as e:
            db.session.rollback()
            logger.error(f"❌ Error deleting customer {customer_id}: {str(e)}")
            raise Exception(f"Failed to delete customer and associated data: {str(e)}")

    def bulk_delete_customers(self, customer_ids: List[int]) -> Tuple[int, int]:
        """
        Delete multiple customers by their IDs.

        Args:
            customer_ids: List of customer IDs to delete

        Returns:
            Tuple of (deleted_count, failed_count)
        """
        deleted_count = 0
        failed_count = 0

        for customer_id in customer_ids:
            try:
                self.delete_customer(customer_id)
                deleted_count += 1
            except Exception as e:
                logger.error(f"Failed to delete customer {customer_id}: {str(e)}")
                failed_count += 1

        # Clear cache for all customers
        clear_cache_for_entity('customer')

        return deleted_count, failed_count

    def get_total_customer_count(self) -> int:
        """
        Get the total number of customers in the database.

        Returns:
            int: Total number of customers
        """
        return Customer.query.count()

    def delete_all_customers(self) -> Tuple[int, int]:
        """
        Delete ALL customers in the database.

        Returns:
            Tuple of (deleted_count, failed_count)
        """
        # Get all customer IDs
        all_customers = Customer.query.with_entities(Customer.id).all()
        customer_ids = [customer.id for customer in all_customers]

        if not customer_ids:
            return 0, 0

        logger.info(f"Starting deletion of all {len(customer_ids)} customers")

        # Use the existing bulk delete method for consistency
        deleted_count, failed_count = self.bulk_delete_customers(customer_ids)

        logger.info(f"Completed deletion of all customers: {deleted_count} deleted, {failed_count} failed")

        return deleted_count, failed_count

    def search_customers_by_name(self, search_term: str) -> List[Dict]:
        """Search for customers by name.

        Args:
            search_term: The search term to look for in customer names.

        Returns:
            A list of customer dictionaries matching the search term.
        """
        customers = self.customer_repo.search_by_name(search_term)
        return [customer.to_dict() for customer in customers]
